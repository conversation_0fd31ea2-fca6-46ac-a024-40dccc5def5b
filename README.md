# Onta 商户管理后台

这是一个使用现代化技术栈构建的商户管理后台前端项目。

## 技术栈

- **React** - 用户界面库
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具
- **TanStack Router** - 类型安全的路由库
- **Zustand** - 简单的状态管理库
- **TanStack Query** - 数据获取和缓存库
- **Tailwind CSS** - 实用优先的 CSS 框架
- **DaisyUI** - 基于 Tailwind CSS 的组件库

## 项目结构

```
src/
├── api/          # API 请求函数
├── assets/       # 静态资源
├── components/   # 可复用组件
├── hooks/        # 自定义 React hooks
├── layouts/      # 布局组件
├── pages/        # 页面组件
├── stores/       # Zustand 状态管理
├── types/        # TypeScript 类型定义
├── utils/        # 工具函数
├── App.tsx       # 应用入口组件
├── main.tsx      # 应用入口文件
├── router.tsx    # 路由配置
└── index.css     # 全局样式
```

## 功能演示

项目包含两个主要演示：

1. **Zustand 状态管理** - 一个计数器示例，展示如何使用 Zustand 管理全局状态
2. **React Query 数据获取** - 一个待办事项列表示例，展示如何使用 React Query 获取和管理远程数据

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 状态管理最佳实践 (Zustand)

### 创建 Store

```typescript
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

type MyState = {
  // 状态
  count: number;
  // 操作
  increment: () => void;
};

export const useMyStore = create<MyState>(
  immer((set) => ({
    count: 0,
    increment: () => set((state) => { state.count += 1 }),
  }))
);
```

### 在组件中使用

```typescript
import { useMyStore } from '../stores/myStore';

function MyComponent() {
  const { count, increment } = useMyStore();
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Increment</button>
    </div>
  );
}
```

## 数据获取最佳实践 (React Query)

### 创建 API 函数

```typescript
export const fetchData = async (): Promise<Data[]> => {
  const response = await fetch('https://api.example.com/data');
  if (!response.ok) {
    throw new Error('Failed to fetch data');
  }
  return response.json();
};
```

### 创建自定义 Hook

```typescript
import { useQuery } from '@tanstack/react-query';
import { fetchData } from '../api/dataApi';

export const useData = () => {
  return useQuery({
    queryKey: ['data'],
    queryFn: fetchData,
  });
};
```

### 在组件中使用

```typescript
import { useData } from '../hooks/useData';

function DataComponent() {
  const { data, isLoading, isError } = useData();
  
  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error loading data</div>;
  
  return (
    <div>
      {data.map((item) => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

## 许可证

MIT
