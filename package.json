{"name": "onta-merchant-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "@tanstack/react-router": "^1.130.12", "@tanstack/router-devtools": "^1.130.13", "autoprefixer": "^10.4.21", "daisyui": "^5.0.50", "immer": "^10.1.1", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}