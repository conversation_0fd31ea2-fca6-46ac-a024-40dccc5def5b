import { createRootRoute, createRoute, createRouter } from '@tanstack/react-router';
import { QueryClient } from '@tanstack/react-query';

// 导入页面组件
import App from './App';
import { TodosPage } from './pages/TodosPage';
import { CounterPage } from './pages/CounterPage';
import { HomePage } from './pages/HomePage';

// 创建一个QueryClient实例
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5分钟
      gcTime: 1000 * 60 * 60, // 1小时
    },
  },
});

// 创建根路由
const rootRoute = createRootRoute({
  component: App,
});

// 创建首页路由
const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: HomePage,
});

// 创建计数器示例页面路由
const counterRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/counter',
  component: CounterPage,
});

// 创建待办事项列表页面路由
const todosRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/todos',
  component: TodosPage,
});

// 创建路由器
const routeTree = rootRoute.addChildren([
  homeRoute,
  counterRoute,
  todosRoute,
]);

export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  // 可选：添加自定义上下文
  context: {
    queryClient,
  },
});

// 声明路由类型
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}