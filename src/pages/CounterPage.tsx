import { Link } from '@tanstack/react-router';
import { useCounterStore } from '../stores/counterStore';
import { useState } from 'react';

export function CounterPage() {
  // 从Zustand store中获取状态和操作
  const { count, increment, decrement, reset, incrementBy } = useCounterStore();
  
  // 本地状态用于自定义增量值
  const [incrementValue, setIncrementValue] = useState(5);

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link to="/" className="btn btn-ghost mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
          </svg>
          返回
        </Link>
        <h1 className="text-3xl font-bold">Zustand 状态管理示例</h1>
      </div>

      <div className="card bg-base-100 shadow-xl max-w-2xl mx-auto">
        <div className="card-body">
          <h2 className="card-title text-2xl mb-6">计数器</h2>
          
          <div className="stats shadow mb-6">
            <div className="stat">
              <div className="stat-title">当前计数</div>
              <div className="stat-value text-primary text-center text-5xl">{count}</div>
              <div className="stat-desc">使用Zustand管理的全局状态</div>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4 justify-center mb-6">
            <button onClick={decrement} className="btn btn-outline">
              减少 (-1)
            </button>
            <button onClick={increment} className="btn btn-outline">
              增加 (+1)
            </button>
            <button onClick={reset} className="btn btn-warning">
              重置
            </button>
          </div>
          
          <div className="divider">自定义增量</div>
          
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
            <div className="form-control">
              <label className="label">
                <span className="label-text">增量值</span>
              </label>
              <input
                type="number"
                value={incrementValue}
                onChange={(e) => setIncrementValue(Number(e.target.value))}
                className="input input-bordered w-24"
              />
            </div>
            <button
              onClick={() => incrementBy(incrementValue)}
              className="btn btn-primary self-end"
            >
              增加 (+{incrementValue})
            </button>
          </div>
          
          <div className="mt-8 bg-base-200 p-4 rounded-lg">
            <h3 className="font-bold mb-2">Zustand 特点</h3>
            <ul className="list-disc list-inside space-y-2">
              <li>轻量级，bundle size 小</li>
              <li>使用简单，无需 Provider 包装</li>
              <li>支持中间件（如 immer、persist 等）</li>
              <li>支持 TypeScript</li>
              <li>可以在任何地方使用，不仅限于 React 组件</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}