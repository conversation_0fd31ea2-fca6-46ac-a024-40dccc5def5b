import { Link } from '@tanstack/react-router';
import { useState } from 'react';
import { useTodos, useCreateTodo, useUpdateTodo, useDeleteTodo } from '../hooks/useTodos';
import { Todo } from '../api/todoApi';

export function TodosPage() {
  const [newTodoTitle, setNewTodoTitle] = useState('');
  
  // 使用React Query hooks
  const todosQuery = useTodos();
  const createTodoMutation = useCreateTodo();
  const updateTodoMutation = useUpdateTodo();
  const deleteTodoMutation = useDeleteTodo();
  
  // 处理创建新待办事项
  const handleCreateTodo = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTodoTitle.trim()) return;
    
    createTodoMutation.mutate({
      title: newTodoTitle,
      completed: false,
      userId: 1, // 示例用户ID
    }, {
      onSuccess: () => {
        setNewTodoTitle('');
      }
    });
  };
  
  // 处理切换待办事项完成状态
  const handleToggleTodo = (todo: Todo) => {
    updateTodoMutation.mutate({
      ...todo,
      completed: !todo.completed
    });
  };
  
  // 处理删除待办事项
  const handleDeleteTodo = (id: number) => {
    deleteTodoMutation.mutate(id);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link to="/" className="btn btn-ghost mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
          </svg>
          返回
        </Link>
        <h1 className="text-3xl font-bold">React Query 示例</h1>
      </div>

      <div className="card bg-base-100 shadow-xl max-w-3xl mx-auto">
        <div className="card-body">
          <h2 className="card-title text-2xl mb-6">待办事项列表</h2>
          
          {/* 添加新待办事项表单 */}
          <form onSubmit={handleCreateTodo} className="flex gap-2 mb-6">
            <input
              type="text"
              value={newTodoTitle}
              onChange={(e) => setNewTodoTitle(e.target.value)}
              placeholder="添加新待办事项..."
              className="input input-bordered flex-1"
              disabled={createTodoMutation.isPending}
            />
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={createTodoMutation.isPending || !newTodoTitle.trim()}
            >
              {createTodoMutation.isPending ? (
                <span className="loading loading-spinner loading-sm"></span>
              ) : '添加'}
            </button>
          </form>
          
          {/* 加载状态 */}
          {todosQuery.isLoading && (
            <div className="flex justify-center my-8">
              <span className="loading loading-spinner loading-lg"></span>
            </div>
          )}
          
          {/* 错误状态 */}
          {todosQuery.isError && (
            <div className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
              <span>加载待办事项失败，请重试。</span>
            </div>
          )}
          
          {/* 待办事项列表 */}
          {todosQuery.isSuccess && (
            <div className="overflow-x-auto">
              <table className="table table-zebra">
                <thead>
                  <tr>
                    <th>状态</th>
                    <th>标题</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {todosQuery.data.slice(0, 10).map((todo) => (
                    <tr key={todo.id} className={todo.completed ? 'opacity-60' : ''}>
                      <td>
                        <input
                          type="checkbox"
                          className="checkbox"
                          checked={todo.completed}
                          onChange={() => handleToggleTodo(todo)}
                          disabled={updateTodoMutation.isPending}
                        />
                      </td>
                      <td className={todo.completed ? 'line-through' : ''}>
                        {todo.title}
                      </td>
                      <td>
                        <button
                          onClick={() => handleDeleteTodo(todo.id)}
                          className="btn btn-error btn-sm"
                          disabled={deleteTodoMutation.isPending}
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <div className="text-sm text-gray-500 mt-4">
                注：仅显示前10条数据，这是一个使用 jsonplaceholder API 的演示。
              </div>
            </div>
          )}
          
          <div className="mt-8 bg-base-200 p-4 rounded-lg">
            <h3 className="font-bold mb-2">React Query 特点</h3>
            <ul className="list-disc list-inside space-y-2">
              <li>自动缓存和重新验证数据</li>
              <li>处理加载和错误状态</li>
              <li>支持乐观更新</li>
              <li>支持分页和无限滚动</li>
              <li>内置开发者工具</li>
              <li>支持服务器端渲染</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}