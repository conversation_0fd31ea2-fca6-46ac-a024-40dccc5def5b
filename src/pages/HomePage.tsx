import { Link } from '@tanstack/react-router';

export function HomePage() {
  return (
    <div className="container mx-auto p-4">
      <div className="hero bg-base-200 rounded-lg">
        <div className="hero-content text-center py-10">
          <div className="max-w-md">
            <h1 className="text-5xl font-bold">Onta 商户管理后台</h1>
            <p className="py-6">
              这是一个使用 React、TanStack Router、Zustand 和 React Query 构建的现代化商户管理后台示例。
            </p>
            <div className="flex gap-4 justify-center">
              <Link to="/counter" className="btn btn-primary">
                Zustand 示例
              </Link>
              <Link to="/todos" className="btn btn-secondary">
                React Query 示例
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Zustand 状态管理</h2>
            <p>
              Zustand 是一个轻量级的状态管理库，使用简单的 API 来管理应用状态。
              它不需要 Provider，可以在任何地方使用，并且支持中间件。
            </p>
            <div className="card-actions justify-end">
              <Link to="/counter" className="btn btn-primary btn-sm">
                查看示例
              </Link>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">React Query 数据获取</h2>
            <p>
              React Query 是一个用于获取、缓存和更新异步数据的库。
              它使管理服务器状态变得简单，并减少了样板代码。
            </p>
            <div className="card-actions justify-end">
              <Link to="/todos" className="btn btn-secondary btn-sm">
                查看示例
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}