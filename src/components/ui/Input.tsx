import { forwardRef } from 'react';
import { clsx } from 'clsx';

// 输入框大小类型
export type InputSize = 'xs' | 'sm' | 'md' | 'lg';

// 输入框状态类型
export type InputState = 'info' | 'success' | 'warning' | 'error';

// 输入框属性接口
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  size?: InputSize;
  state?: InputState;
  bordered?: boolean;
  ghost?: boolean;
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

// 输入框组件
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      size = 'md',
      state,
      bordered = true,
      ghost = false,
      label,
      helperText,
      error,
      leftIcon,
      rightIcon,
      className,
      disabled = false,
      ...props
    },
    ref
  ) => {
    const inputClasses = clsx(
      'input',
      {
        // 大小样式
        'input-xs': size === 'xs',
        'input-sm': size === 'sm',
        'input-lg': size === 'lg',
        
        // 状态样式
        'input-info': state === 'info',
        'input-success': state === 'success',
        'input-warning': state === 'warning',
        'input-error': state === 'error' || !!error,
        
        // 修饰符样式
        'input-bordered': bordered,
        'input-ghost': ghost,
        
        // 图标样式
        'pl-10': !!leftIcon,
        'pr-10': !!rightIcon,
      },
      className
    );

    const wrapperClasses = clsx('form-control w-full', {
      'opacity-50': disabled,
    });

    return (
      <div className={wrapperClasses}>
        {label && (
          <label className="label">
            <span className="label-text">{label}</span>
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className="h-5 w-5 text-gray-400">{leftIcon}</div>
            </div>
          )}
          
          <input
            ref={ref}
            className={inputClasses}
            disabled={disabled}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <div className="h-5 w-5 text-gray-400">{rightIcon}</div>
            </div>
          )}
        </div>
        
        {(helperText || error) && (
          <label className="label">
            <span className={clsx('label-text-alt', {
              'text-error': !!error,
            })}>
              {error || helperText}
            </span>
          </label>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';
