import { useEffect, useRef } from 'react';
import { clsx } from 'clsx';

// 模态框属性接口
export interface ModalProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
  maskClosable?: boolean;
  className?: string;
}

// 模态框组件
export function Modal({
  open,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  closable = true,
  maskClosable = true,
  className,
}: ModalProps) {
  const modalRef = useRef<HTMLDialogElement>(null);

  useEffect(() => {
    const modal = modalRef.current;
    if (!modal) return;

    if (open) {
      modal.showModal();
    } else {
      modal.close();
    }
  }, [open]);

  const handleClose = () => {
    if (closable) {
      onClose();
    }
  };

  const handleMaskClick = (e: React.MouseEvent) => {
    if (maskClosable && e.target === e.currentTarget) {
      handleClose();
    }
  };

  const modalClasses = clsx(
    'modal',
    {
      'modal-open': open,
    }
  );

  const boxClasses = clsx(
    'modal-box',
    {
      'w-11/12 max-w-sm': size === 'sm',
      'w-11/12 max-w-md': size === 'md',
      'w-11/12 max-w-2xl': size === 'lg',
      'w-11/12 max-w-5xl': size === 'xl',
    },
    className
  );

  return (
    <dialog
      ref={modalRef}
      className={modalClasses}
      onClose={handleClose}
      onClick={handleMaskClick}
    >
      <div className={boxClasses} onClick={(e) => e.stopPropagation()}>
        {/* 标题栏 */}
        {(title || closable) && (
          <div className="flex items-center justify-between mb-4">
            {title && <h3 className="font-bold text-lg">{title}</h3>}
            {closable && (
              <button
                className="btn btn-sm btn-circle btn-ghost"
                onClick={handleClose}
              >
                ✕
              </button>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div className="py-4">
          {children}
        </div>

        {/* 底部操作区 */}
        {footer && (
          <div className="modal-action">
            {footer}
          </div>
        )}
      </div>
    </dialog>
  );
}

// 确认对话框属性接口
export interface ConfirmModalProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title?: string;
  content: React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  confirmButtonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  cancelButtonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
}

// 确认对话框组件
export function ConfirmModal({
  open,
  onConfirm,
  onCancel,
  title = '确认',
  content,
  confirmText = '确认',
  cancelText = '取消',
  confirmButtonProps,
  cancelButtonProps,
}: ConfirmModalProps) {
  const footer = (
    <>
      <button
        className="btn"
        onClick={onCancel}
        {...cancelButtonProps}
      >
        {cancelText}
      </button>
      <button
        className="btn btn-primary"
        onClick={onConfirm}
        {...confirmButtonProps}
      >
        {confirmText}
      </button>
    </>
  );

  return (
    <Modal
      open={open}
      onClose={onCancel}
      title={title}
      footer={footer}
      size="sm"
      maskClosable={false}
    >
      <div className="text-center">
        {content}
      </div>
    </Modal>
  );
}
