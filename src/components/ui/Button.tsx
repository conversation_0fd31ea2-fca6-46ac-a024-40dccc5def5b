import { forwardRef } from 'react';
import { clsx } from 'clsx';

// 按钮变体类型
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'accent' 
  | 'ghost' 
  | 'link' 
  | 'info' 
  | 'success' 
  | 'warning' 
  | 'error';

// 按钮大小类型
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg';

// 按钮属性接口
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  outline?: boolean;
  wide?: boolean;
  block?: boolean;
  square?: boolean;
  circle?: boolean;
  loading?: boolean;
  active?: boolean;
  children?: React.ReactNode;
}

// 按钮组件
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      outline = false,
      wide = false,
      block = false,
      square = false,
      circle = false,
      loading = false,
      active = false,
      disabled = false,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const classes = clsx(
      'btn',
      {
        // 变体样式
        'btn-primary': variant === 'primary',
        'btn-secondary': variant === 'secondary',
        'btn-accent': variant === 'accent',
        'btn-ghost': variant === 'ghost',
        'btn-link': variant === 'link',
        'btn-info': variant === 'info',
        'btn-success': variant === 'success',
        'btn-warning': variant === 'warning',
        'btn-error': variant === 'error',
        
        // 大小样式
        'btn-xs': size === 'xs',
        'btn-sm': size === 'sm',
        'btn-lg': size === 'lg',
        
        // 修饰符样式
        'btn-outline': outline,
        'btn-wide': wide,
        'btn-block': block,
        'btn-square': square,
        'btn-circle': circle,
        'btn-active': active,
        'btn-disabled': disabled,
      },
      className
    );

    return (
      <button
        ref={ref}
        className={classes}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <span className="loading loading-spinner loading-sm mr-2"></span>
        )}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';
