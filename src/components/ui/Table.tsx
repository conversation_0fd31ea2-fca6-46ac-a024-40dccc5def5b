import { clsx } from 'clsx';
import { TableColumn } from '../../types';

// 表格属性接口
export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  empty?: React.ReactNode;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  zebra?: boolean;
  hover?: boolean;
  compact?: boolean;
  className?: string;
  rowKey?: keyof T | ((record: T, index: number) => string | number);
  onRowClick?: (record: T, index: number) => void;
}

// 表格组件
export function Table<T = any>({
  columns,
  data,
  loading = false,
  empty,
  size = 'md',
  zebra = true,
  hover = true,
  compact = false,
  className,
  rowKey = 'id',
  onRowClick,
}: TableProps<T>) {
  const tableClasses = clsx(
    'table w-full',
    {
      'table-xs': size === 'xs',
      'table-sm': size === 'sm',
      'table-lg': size === 'lg',
      'table-zebra': zebra,
      'table-hover': hover,
      'table-compact': compact,
    },
    className
  );

  const getRowKey = (record: T, index: number): string | number => {
    if (typeof rowKey === 'function') {
      return rowKey(record, index);
    }
    return record[rowKey] as string | number;
  };

  const handleRowClick = (record: T, index: number) => {
    if (onRowClick) {
      onRowClick(record, index);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {empty || '暂无数据'}
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className={tableClasses}>
        <thead>
          <tr>
            {columns.map((column, index) => (
              <th
                key={index}
                style={{ width: column.width }}
                className={clsx({
                  'cursor-pointer hover:bg-base-200': column.sortable,
                })}
              >
                {column.title}
                {column.sortable && (
                  <span className="ml-1">
                    <svg
                      className="w-4 h-4 inline"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                      />
                    </svg>
                  </span>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((record, index) => (
            <tr
              key={getRowKey(record, index)}
              className={clsx({
                'cursor-pointer': !!onRowClick,
              })}
              onClick={() => handleRowClick(record, index)}
            >
              {columns.map((column, colIndex) => (
                <td key={colIndex}>
                  {column.render
                    ? column.render(record[column.key], record, index)
                    : String(record[column.key] || '')}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// 分页表格属性接口
export interface PaginatedTableProps<T = any> extends TableProps<T> {
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

// 分页表格组件
export function PaginatedTable<T = any>({
  pagination,
  ...tableProps
}: PaginatedTableProps<T>) {
  if (!pagination) {
    return <Table {...tableProps} />;
  }

  const { current, pageSize, total, onChange } = pagination;
  const totalPages = Math.ceil(total / pageSize);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onChange(page, pageSize);
    }
  };

  return (
    <div>
      <Table {...tableProps} />
      
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <div className="join">
            <button
              className="join-item btn"
              disabled={current <= 1}
              onClick={() => handlePageChange(current - 1)}
            >
              «
            </button>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                className={clsx('join-item btn', {
                  'btn-active': page === current,
                })}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </button>
            ))}
            
            <button
              className="join-item btn"
              disabled={current >= totalPages}
              onClick={() => handlePageChange(current + 1)}
            >
              »
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
