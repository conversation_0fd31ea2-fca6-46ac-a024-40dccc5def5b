import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import { RouterProvider } from '@tanstack/react-router';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// 导入路由和查询客户端
import { router, queryClient } from './router';

// 在开发环境中注册路由开发工具
import { routerDevtools } from './routerDevtools';

// 创建根元素
const rootElement = document.getElementById('root')!;
const root = createRoot(rootElement);

// 渲染应用
root.render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  </StrictMode>,
)
