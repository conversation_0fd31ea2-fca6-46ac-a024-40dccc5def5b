// 通用类型定义

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 分页参数
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 表单状态
export interface FormState {
  isSubmitting: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

// 用户信息
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
}

// 用户角色
export enum UserRole {
  ADMIN = 'admin',
  MERCHANT = 'merchant',
  USER = 'user',
}

// 商户信息
export interface Merchant {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  status: MerchantStatus;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

// 商户状态
export enum MerchantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

// 订单信息
export interface Order {
  id: string;
  merchantId: string;
  userId: string;
  amount: number;
  currency: string;
  status: OrderStatus;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

// 订单状态
export enum OrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

// 订单项
export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  total: number;
}

// 产品信息
export interface Product {
  id: string;
  merchantId: string;
  name: string;
  description?: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  stock: number;
  status: ProductStatus;
  createdAt: string;
  updatedAt: string;
}

// 产品状态
export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  OUT_OF_STOCK = 'out_of_stock',
  DISCONTINUED = 'discontinued',
}

// 通用选项类型
export interface Option<T = string> {
  label: string;
  value: T;
  disabled?: boolean;
}

// 表格列定义
export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  width?: number;
  sortable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

// 排序参数
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

// 筛选参数
export interface FilterParams {
  [key: string]: any;
}

// 搜索参数
export interface SearchParams extends PaginationParams {
  keyword?: string;
  sort?: SortParams;
  filters?: FilterParams;
}
